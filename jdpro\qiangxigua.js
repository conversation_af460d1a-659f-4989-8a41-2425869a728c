const $ = new Env('抢西瓜');
const notify = $.isNode() ? require('./sendNotify') : '';
//Node.js用户请在jdCookie.js处填写京东ck;
const jdCookieNode = $.isNode() ? require('./jdCookie.js') : '';
const getH5st = require('./function/getH5st3_0');
const { sendMessage } = require('./utils/sendMessage');
let cookiesArr = [], cookie = '', message;

if ($.isNode()) {
  Object.keys(jdCookieNode).forEach((item) => {
    cookiesArr.push(jdCookieNode[item])
  })
  if (process.env.JD_DEBUG && process.env.JD_DEBUG === 'false') console.log = () => {};
  if (JSON.stringify(process.env).indexOf('GITHUB') > -1) process.exit(0);
} else {
  cookiesArr = [$.getdata('CookieJD'), $.getdata('CookieJD2'), ...jsonParse($.getdata('CookiesJD') || "[]").map(item => item.cookie)].filter(item => !!item);
}

!(async () => {
    if (!cookiesArr[0]) {
        $.msg($.name, '【提示】请先获取京东账号一cookie\n直接使用NobyDa的京东签到获取', 'https://bean.m.jd.com/bean/signIndex.action', { "open-url": "https://bean.m.jd.com/bean/signIndex.action" });
        return;
    }


    for (let i = 0; i < cookiesArr.length; i++) {
        if (cookiesArr[i]) {
            cookie = cookiesArr[i];

            $.UserName = decodeURIComponent(cookie.match(/pt_pin=([^; ]+)(?=;?)/) && cookie.match(/pt_pin=([^; ]+)(?=;?)/)[1])
            $.index = i + 1;
            $.isLogin = true;
            $.nickName = '';
            message = '';
            console.log(`\n******开始【京东账号${$.index}】${$.nickName || $.UserName}*********\n`);
            if (!$.isLogin) {
                $.msg($.name, `【提示】cookie已失效`, `京东账号${$.index} ${$.nickName || $.UserName}\n请重新登录获取\nhttps://bean.m.jd.com/bean/signIndex.action`, { "open-url": "https://bean.m.jd.com/bean/signIndex.action" });

                if ($.isNode()) {
                    await notify.sendNotify(`${$.name}cookie已失效 - ${$.UserName}`, `京东账号${$.index} ${$.UserName}\n请重新登录获取cookie`);
                }
                continue
            }
            const {productId, token} = await atopChannelBlack5Products()
            await atopCartBlack5RushBuy(productId, token)
            await $.wait(1000)
        }
    }
})()
  .catch((e) => {
    $.log('', `❌ ${$.name}, 失败! 原因: ${e}!`, '')
  })
  .finally(() => {
    $.done();
  })

// 工具函数
function safeGet(data) {
  try {
    if (typeof JSON.parse(data) == "object") {
      return true;
    }
  } catch (e) {
    console.log(e);
    console.log(`京东服务器访问数据为空，请检查自身设备网络情况`);
    return false;
  }
}

function jsonParse(str) {
  if (typeof str == "string") {
    try {
      return JSON.parse(str);
    } catch (e) {
      console.log(e);
      $.msg($.name, '', '请勿随意在BoxJs输入框修改内容\n建议通过脚本去获取cookie')
      return [];
    }
  }
}

// UUID生成函数
function getUUID(format = 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', UpperCase = 0) {
    return format.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        var uuid;
        if (UpperCase) {
            uuid = v.toString(36).toUpperCase();
        } else {
            uuid = v.toString(36)
        }
        return uuid;
    });
}


// 获取必要的token
// 参考jd_js_cash.js的sign函数结构编写的API请求函数
async function atopChannelBlack5Products() {
    return new Promise(async resolve => {
        // 构建请求体数据
        const bodyData = {
            "bizCode": "cn_retail_fresh",
            "scenario": "default",
            "provinceId": 15,
            "cityId": 1262,
            "countyId": 1266,
            "townId": 57928,
            "lat": 29.340082,
            "lng": 120.120766,
            "babelChannel": "ttt165",
            "isJdApp": "1",
            "isWx": "0"
        };

        // 动态生成h5st参数
        let h5st;
        try {
            // 根据混淆代码分析，getH5st函数接受三个参数：appId, 参数对象, version
            const h5stParams = {
                appid: "activities_platform",  // 添加appid字段
                functionId: "atop_channel_black5_products",
                body: JSON.stringify(bodyData),
                client: "android",
                clientVersion: "11.1.0",
                t: Date.now()
            };

            h5st = await getH5st("activities_platform", h5stParams, "3.0");
            if (!h5st) {
                console.log('生成h5st失败，使用备用方案');
                resolve({ success: false, error: '生成h5st失败', rawData: null });
                return;
            }
        } catch (e) {
            console.log('生成h5st异常:', e);
            resolve({ success: false, error: '生成h5st异常', rawData: null });
            return;
        }

        // 动态生成UUID
        const dynamicUuid = getUUID();

        const options = {
            url: `https://api.m.jd.com/atop_channel_black5_products`,
            body: `appid=jd-super-market&t=${Date.now()}&functionId=atop_channel_black5_products&client=m&uuid=${dynamicUuid}&h5st=${h5st}&x-api-eid-token=jdd03I3SPKQAPZXIQVN3H3SSC4BKLIMXZW4MQZPGAQ3XMVI76QX6FTDEB4I7NKZ7Z43NG25FCV574UP76NFI2X3X6ZMT2PUAAAAMXCSTF4VAAAAAACSJS66ALAKL2RAX&body=${encodeURIComponent(JSON.stringify(bodyData))}`,
            headers: {
                'Cookie': cookie,
                "Host": "api.m.jd.com",
                'Origin': 'https://prodev.m.jd.com',
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "*/*",
                "Connection": "keep-alive",
                "User-Agent": "jdapp;iPhone;15.1.35;;;M/5.0;appBuild/169878;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22ZJUzZwC1DJS2EWS2Y2Y1CWG0DwO2CWVuYwO1YzK0DJdwYtruZwS3Dm%3D%3D%22%2C%22sv%22%3A%22CJSkDI43%22%2C%22iad%22%3A%22GJTND0GzDNcjHtGnGs00Gzc1BJunGzKjG0SyCNZLCtY5DJKn%22%7D%2C%22ts%22%3A1748398122%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 12_5_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;",
                "Accept-Language": "zh-cn",
                'Referer': `https://prodev.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html`,
                "Accept-Encoding": "br, gzip, deflate",
                'x-rp-client': 'h5_1.0.0',
                'cache-control': 'max-age=0',
                'scenario': 'default',
                'bizcode': 'cn_retail_fresh',
                'x-referer-page': 'https://prodev.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html'
            }
        }

        $.post(options, async (err, resp, data) => {
            try {
                if (err) {
                    console.log(`${JSON.stringify(err)}`)
                    console.log(`${$.name} API请求失败，请检查网路重试`)
                } else {
                    if (safeGet(data)) {
                        data = $.toObj(data);
                        // 修复判断逻辑：code可能是字符串"0"或数字0，success为true表示成功
                        if (data.code === 0 || data.code === "0" || data.success === true) {
                            message += `抢西瓜活动：请求成功\n`;
                            console.log(`抢西瓜活动：请求成功\n`);
                            // console.log(`返回数据：${JSON.stringify(data.data)}`);

                            // 解析商品信息并提取productId和token
                            let productList = [];
                            if (data.data && data.data.floorData && data.data.floorData.items) {
                                const items = data.data.floorData.items;
                                items.forEach(item => {
                                    if (item.current && item.current.products) {
                                        console.log(`会话名称：${item.current.sessionName}`);
                                        item.current.products.forEach(product => {
                                            console.log(`商品：${product.skuShortName} - 原价：${product.skuPrice} - 优惠价：${product.discountPrice} - 预约人数：${product.subscribeNum}`);
                                            // 收集productId和token
                                            productList.push({
                                                productId: product.productId,
                                                token: product.token,
                                                skuShortName: product.skuShortName,
                                                skuPrice: product.skuPrice,
                                                discountPrice: product.discountPrice,
                                                subscribeNum: product.subscribeNum
                                            });
                                        });
                                    }
                                });
                            }

                            // 查找西瓜商品
                            const watermelonProduct = productList.find(product =>
                                product.skuShortName && product.skuShortName.includes('西瓜')
                            );

                            // 返回包含productId和token的结果，支持解构
                            const result = {
                                success: true,
                                products: productList,
                                // 为了方便解构，提供西瓜商品的productId和token
                                productId: watermelonProduct ? watermelonProduct.productId : null,
                                token: watermelonProduct ? watermelonProduct.token : null,
                                watermelonProduct: watermelonProduct, // 西瓜商品的完整信息
                                rawData: data
                            };
                            resolve(result);
                            return;
                        } else {
                            console.log(`抢西瓜活动：请求失败:${data.message || data.msg || '未知错误'}\n`);
                            resolve({ success: false, error: data.message || data.msg || '未知错误', rawData: data });
                            return;
                        }
                    }
                }
            } catch (e) {
                $.logErr(e, resp)
                resolve({ success: false, error: e.message, rawData: null });
            } finally {
                // 如果没有在try块中resolve，这里resolve一个默认的失败结果
                if (!data) {
                    resolve({ success: false, error: '请求失败', rawData: null });
                }
            }
        })
    })
}


// 抢购商品API方法
async function atopCartBlack5RushBuy(productId, token) {
    return new Promise(async resolve => {
        // 构建请求体数据
        const bodyData = {
            "bizCode": "cn_retail_fresh",
            "scenario": "default",
            "productId": productId,
            "token": token,
            "provinceId": 15,
            "cityId": 1262,
            "countyId": 1266,
            "townId": 57928,
            "lat": 29.340082,
            "lng": 120.120766,
            "babelChannel": "ttt165",
            "isJdApp": "1",
            "isWx": "0"
        };

        // 动态生成h5st参数
        let h5st;
        try {
            // 根据混淆代码分析，getH5st函数接受三个参数：appId, 参数对象, version
            const h5stParams = {
                appid: "activities_platform",  // 添加appid字段
                functionId: "atop_cart_black5_rushBuy",
                body: JSON.stringify(bodyData),
                client: "android",
                clientVersion: "11.1.0",
                t: Date.now()
            };

            h5st = await getH5st("activities_platform", h5stParams, "3.0");
            if (!h5st) {
                console.log('生成抢购h5st失败，使用备用方案');
                resolve({ success: false, error: '生成h5st失败', rawData: null });
                return;
            }
        } catch (e) {
            console.log('生成抢购h5st异常:', e);
            resolve({ success: false, error: '生成h5st异常', rawData: null });
            return;
        }

        // 动态生成UUID
        const dynamicUuid = getUUID();

        const options = {
            url: `https://api.m.jd.com/atop_cart_black5_rushBuy`,
            body: `appid=jd-super-market&t=${Date.now()}&functionId=atop_cart_black5_rushBuy&client=m&uuid=${dynamicUuid}&h5st=${h5st}&x-api-eid-token=jdd03I3SPKQAPZXIQVN3H3SSC4BKLIMXZW4MQZPGAQ3XMVI76QX6FTDEB4I7NKZ7Z43NG25FCV574UP76NFI2X3X6ZMT2PUAAAAMXCSTF4VAAAAAACSJS66ALAKL2RAX&body=${encodeURIComponent(JSON.stringify(bodyData))}`,
            headers: {
                'Cookie': cookie,
                "Host": "api.m.jd.com",
                'Origin': 'https://prodev.m.jd.com',
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "*/*",
                "Connection": "keep-alive",
                "User-Agent": "jdapp;iPhone;15.1.35;;;M/5.0;appBuild/169878;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22ZJUzZwC1DJS2EWS2Y2Y1CWG0DwO2CWVuYwO1YzK0DJdwYtruZwS3Dm%3D%3D%22%2C%22sv%22%3A%22CJSkDI43%22%2C%22iad%22%3A%22GJTND0GzDNcjHtGnGs00Gzc1BJunGzKjG0SyCNZLCtY5DJKn%22%7D%2C%22ts%22%3A1748398122%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D;Mozilla/5.0 (iPhone; CPU iPhone OS 12_5_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148;supportJDSHWK/1;",
                "Accept-Language": "zh-cn",
                'Referer': `https://prodev.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html`,
                "Accept-Encoding": "br, gzip, deflate",
                'x-rp-client': 'h5_1.0.0',
                'scenario': 'default',
                'bizcode': 'cn_retail_fresh',
                'x-referer-page': 'https://prodev.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html'
            }
        }

        $.post(options, async (err, resp, data) => {
            try {
                if (err) {
                    console.log(`${JSON.stringify(err)}`)
                    console.log(`${$.name} 抢购API请求失败，请检查网路重试`)
                } else {
                    if (safeGet(data)) {
                        data = $.toObj(data);
                        // 修复判断逻辑：code可能是字符串"0"或数字0，success为true表示成功
                        if (data.code === 0 || data.code === "0" || data.success === true) {
                            message += `抢购请求：成功\n`;
                            console.log(`抢购请求：成功\n`);
                            console.log(`抢购结果：${JSON.stringify(data)}`);

                            // 解析抢购结果
                            if (data.data) {
                                if (data.data.success) {
                                    console.log(`🎉 抢购成功！商品ID: ${productId}`);
                                    message += `🎉 抢购成功！商品ID: ${productId}\n`;

                                    // 发送抢购成功通知
                                    try {
                                        const notifyMessage = `🎉 京东抢西瓜成功通知\n账号: ${$.UserName}\n商品ID: ${productId}\n时间: ${new Date().toLocaleString()}`;
                                        await sendMessage(notifyMessage);
                                        console.log('抢购成功通知已发送');
                                    } catch (error) {
                                        console.log('发送抢购成功通知失败:', error.message);
                                    }
                                } else {
                                    console.log(`❌ 抢购失败: ${data.data.message || data.data.msg || '未知原因'}`);
                                    message += `❌ 抢购失败: ${data.data.message || data.data.msg || '未知原因'}\n`;
                                }
                            }
                        } else {
                            console.log(`抢购请求：失败:${data.message || data.msg || '未知错误'}\n`);
                        }
                    }
                }
            } catch (e) {
                $.logErr(e, resp)
            } finally {
                resolve(data);
            }
        })
    })
}

//  


// prettier-ignore
function Env(t,e){"undefined"!=typeof process&&JSON.stringify(process.env).indexOf("GITHUB")>-1&&process.exit(0);class s{constructor(t){this.env=t}send(t,e="GET"){t="string"==typeof t?{url:t}:t;let s=this.get;return"POST"===e&&(s=this.post),new Promise((e,i)=>{s.call(this,t,(t,s,r)=>{t?i(t):e(s)})})}get(t){return this.send.call(this.env,t)}post(t){return this.send.call(this.env,t,"POST")}}return new class{constructor(t,e){this.name=t,this.http=new s(this),this.data=null,this.dataFile="box.dat",this.logs=[],this.isMute=!1,this.isNeedRewrite=!1,this.logSeparator="\n",this.startTime=(new Date).getTime(),Object.assign(this,e),this.log("",`🔔${this.name}, 开始!`)}isNode(){return"undefined"!=typeof module&&!!module.exports}isQuanX(){return"undefined"!=typeof $task}isSurge(){return"undefined"!=typeof $httpClient&&"undefined"==typeof $loon}isLoon(){return"undefined"!=typeof $loon}toObj(t,e=null){try{return JSON.parse(t)}catch{return e}}toStr(t,e=null){try{return JSON.stringify(t)}catch{return e}}getjson(t,e){let s=e;const i=this.getdata(t);if(i)try{s=JSON.parse(this.getdata(t))}catch{}return s}setjson(t,e){try{return this.setdata(JSON.stringify(t),e)}catch{return!1}}getScript(t){return new Promise(e=>{this.get({url:t},(t,s,i)=>e(i))})}runScript(t,e){return new Promise(s=>{let i=this.getdata("@chavy_boxjs_userCfgs.httpapi");i=i?i.replace(/\n/g,"").trim():i;let r=this.getdata("@chavy_boxjs_userCfgs.httpapi_timeout");r=r?1*r:20,r=e&&e.timeout?e.timeout:r;const[o,h]=i.split("@"),n={url:`http://${h}/v1/scripting/evaluate`,body:{script_text:t,mock_type:"cron",timeout:r},headers:{"X-Key":o,Accept:"*/*"}};this.post(n,(t,e,i)=>s(i))}).catch(t=>this.logErr(t))}loaddata(){if(!this.isNode())return{};{this.fs=this.fs?this.fs:require("fs"),this.path=this.path?this.path:require("path");const t=this.path.resolve(this.dataFile),e=this.path.resolve(process.cwd(),this.dataFile),s=this.fs.existsSync(t),i=!s&&this.fs.existsSync(e);if(!s&&!i)return{};{const i=s?t:e;try{return JSON.parse(this.fs.readFileSync(i))}catch(t){return{}}}}}writedata(){if(this.isNode()){this.fs=this.fs?this.fs:require("fs"),this.path=this.path?this.path:require("path");const t=this.path.resolve(this.dataFile),e=this.path.resolve(process.cwd(),this.dataFile),s=this.fs.existsSync(t),i=!s&&this.fs.existsSync(e),r=JSON.stringify(this.data);s?this.fs.writeFileSync(t,r):i?this.fs.writeFileSync(e,r):this.fs.writeFileSync(t,r)}}lodash_get(t,e,s){const i=e.replace(/\[(\d+)\]/g,".$1").split(".");let r=t;for(const t of i)if(r=Object(r)[t],void 0===r)return s;return r}lodash_set(t,e,s){return Object(t)!==t?t:(Array.isArray(e)||(e=e.toString().match(/[^.[\]]+/g)||[]),e.slice(0,-1).reduce((t,s,i)=>Object(t[s])===t[s]?t[s]:t[s]=Math.abs(e[i+1])>>0==+e[i+1]?[]:{},t)[e[e.length-1]]=s,t)}getdata(t){let e=this.getval(t);if(/^@/.test(t)){const[,s,i]=/^@(.*?)\.(.*?)$/.exec(t),r=s?this.getval(s):"";if(r)try{const t=JSON.parse(r);e=t?this.lodash_get(t,i,""):e}catch(t){e=""}}return e}setdata(t,e){let s=!1;if(/^@/.test(e)){const[,i,r]=/^@(.*?)\.(.*?)$/.exec(e),o=this.getval(i),h=i?"null"===o?null:o||"{}":"{}";try{const e=JSON.parse(h);this.lodash_set(e,r,t),s=this.setval(JSON.stringify(e),i)}catch(e){const o={};this.lodash_set(o,r,t),s=this.setval(JSON.stringify(o),i)}}else s=this.setval(t,e);return s}getval(t){return this.isSurge()||this.isLoon()?$persistentStore.read(t):this.isQuanX()?$prefs.valueForKey(t):this.isNode()?(this.data=this.loaddata(),this.data[t]):this.data&&this.data[t]||null}setval(t,e){return this.isSurge()||this.isLoon()?$persistentStore.write(t,e):this.isQuanX()?$prefs.setValueForKey(t,e):this.isNode()?(this.data=this.loaddata(),this.data[e]=t,this.writedata(),!0):this.data&&this.data[e]||null}initGotEnv(t){this.got=this.got?this.got:require("got"),this.cktough=this.cktough?this.cktough:require("tough-cookie"),this.ckjar=this.ckjar?this.ckjar:new this.cktough.CookieJar,t&&(t.headers=t.headers?t.headers:{},void 0===t.headers.Cookie&&void 0===t.cookieJar&&(t.cookieJar=this.ckjar))}get(t,e=(()=>{})){t.headers&&(delete t.headers["Content-Type"],delete t.headers["Content-Length"]),this.isSurge()||this.isLoon()?(this.isSurge()&&this.isNeedRewrite&&(t.headers=t.headers||{},Object.assign(t.headers,{"X-Surge-Skip-Scripting":!1})),$httpClient.get(t,(t,s,i)=>{!t&&s&&(s.body=i,s.statusCode=s.status),e(t,s,i)})):this.isQuanX()?(this.isNeedRewrite&&(t.opts=t.opts||{},Object.assign(t.opts,{hints:!1})),$task.fetch(t).then(t=>{const{statusCode:s,statusCode:i,headers:r,body:o}=t;e(null,{status:s,statusCode:i,headers:r,body:o},o)},t=>e(t))):this.isNode()&&(this.initGotEnv(t),this.got(t).on("redirect",(t,e)=>{try{if(t.headers["set-cookie"]){const s=t.headers["set-cookie"].map(this.cktough.Cookie.parse).toString();s&&this.ckjar.setCookieSync(s,null),e.cookieJar=this.ckjar}}catch(t){this.logErr(t)}}).then(t=>{const{statusCode:s,statusCode:i,headers:r,body:o}=t;e(null,{status:s,statusCode:i,headers:r,body:o},o)},t=>{const{message:s,response:i}=t;e(s,i,i&&i.body)}))}post(t,e=(()=>{})){if(t.body&&t.headers&&!t.headers["Content-Type"]&&(t.headers["Content-Type"]="application/x-www-form-urlencoded"),t.headers&&delete t.headers["Content-Length"],this.isSurge()||this.isLoon())this.isSurge()&&this.isNeedRewrite&&(t.headers=t.headers||{},Object.assign(t.headers,{"X-Surge-Skip-Scripting":!1})),$httpClient.post(t,(t,s,i)=>{!t&&s&&(s.body=i,s.statusCode=s.status),e(t,s,i)});else if(this.isQuanX())t.method="POST",this.isNeedRewrite&&(t.opts=t.opts||{},Object.assign(t.opts,{hints:!1})),$task.fetch(t).then(t=>{const{statusCode:s,statusCode:i,headers:r,body:o}=t;e(null,{status:s,statusCode:i,headers:r,body:o},o)},t=>e(t));else if(this.isNode()){this.initGotEnv(t);const{url:s,...i}=t;this.got.post(s,i).then(t=>{const{statusCode:s,statusCode:i,headers:r,body:o}=t;e(null,{status:s,statusCode:i,headers:r,body:o},o)},t=>{const{message:s,response:i}=t;e(s,i,i&&i.body)})}}time(t,e=null){const s=e?new Date(e):new Date;let i={"M+":s.getMonth()+1,"d+":s.getDate(),"H+":s.getHours(),"m+":s.getMinutes(),"s+":s.getSeconds(),"q+":Math.floor((s.getMonth()+3)/3),S:s.getMilliseconds()};/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(s.getFullYear()+"").substr(4-RegExp.$1.length)));for(let e in i)new RegExp("("+e+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?i[e]:("00"+i[e]).substr((""+i[e]).length)));return t}msg(e=t,s="",i="",r){const o=t=>{if(!t)return t;if("string"==typeof t)return this.isLoon()?t:this.isQuanX()?{"open-url":t}:this.isSurge()?{url:t}:void 0;if("object"==typeof t){if(this.isLoon()){let e=t.openUrl||t.url||t["open-url"],s=t.mediaUrl||t["media-url"];return{openUrl:e,mediaUrl:s}}if(this.isQuanX()){let e=t["open-url"]||t.url||t.openUrl,s=t["media-url"]||t.mediaUrl;return{"open-url":e,"media-url":s}}if(this.isSurge()){let e=t.url||t.openUrl||t["open-url"];return{url:e}}}};if(this.isMute||(this.isSurge()||this.isLoon()?$notification.post(e,s,i,o(r)):this.isQuanX()&&$notify(e,s,i,o(r))),!this.isMuteLog){let t=["","==============📣系统通知📣=============="];t.push(e),s&&t.push(s),i&&t.push(i),console.log(t.join("\n")),this.logs=this.logs.concat(t)}}log(...t){t.length>0&&(this.logs=[...this.logs,...t]),console.log(t.join(this.logSeparator))}logErr(t,e){const s=!this.isSurge()&&!this.isQuanX()&&!this.isLoon();s?this.log("",`❗️${this.name}, 错误!`,t.stack):this.log("",`❗️${this.name}, 错误!`,t)}wait(t){return new Promise(e=>setTimeout(e,t))}done(t={}){const e=(new Date).getTime(),s=(e-this.startTime)/1e3;this.log("",`🔔${this.name}, 结束! 🕛 ${s} 秒`),this.log(),(this.isSurge()||this.isQuanX()||this.isLoon())&&$done(t)}}(t,e)}


