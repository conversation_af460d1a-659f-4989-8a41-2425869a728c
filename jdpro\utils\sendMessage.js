var axios = require('axios');

/**
 * 发送消息到指定接收者
 * @param {string} msg - 要发送的消息内容
 * @returns {Promise<Object|null>} 返回响应数据或null
 */
async function sendMessage(msg) {
  try {
    const { status, data } = await axios({
      method: 'POST',
      url: 'http://101.43.31.14:7819/api/SendTxt',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        msg: msg,
        receiver: 'liuliangzheng'
      }
    });

    if (status === 200) {
      return data;
    } else {
      console.error('发送消息失败，状态码:', status);
      return null;
    }
  } catch (error) {
    console.error('发送消息时发生错误:', error.message);
    return null;
  }
}

module.exports.sendMessage = sendMessage;
